<template>
    <PopoverRoot :open="isPopoverOpen" @update:open="handlePopoverToggle">
        <PopoverTrigger aria-label="Select date" asChild class="w-fit" :class="{ 'pointer-events-none': disabled }">
            <div class="relative flex items-center">
                <input
                    ref="dateInputRef"
                    v-model="dateInputValue"
                    type="text"
                    class="form-input"
                    :disabled="disabled"
                    :maxlength="DATE_INPUT_MAX_LENGTH"
                    placeholder="TT.MM.JJJJ"
                    @click.stop
                    @focus="handleInputFocus"
                    @blur="handleInputBlur"
                    @input="handleInputChange" />
                <UiIcon class="absolute right-0 h-8 w-8 pr-3" name="calendar-outlined" />
            </div>
        </PopoverTrigger>

        <PopoverPortal v-if="!disabled" >
            <PopoverContent
                side="bottom"
                class="z-[1000] rounded-sm bg-white p-4 text-sm font-light shadow-xl"
                @openAutoFocus="(e) => e.preventDefault()">
                <!-- Calendar Header -->
                <div class="mb-4 flex items-center justify-between">
                    <UiButtonAction @click="navigateMonth(-1)">
                        <UiIcon name="chevron-left" class="text-blue-600 hover:text-blue-800" />
                    </UiButtonAction>

                    <UiListbox v-model="calendarMonth" class="min-w-[122px]" @update:model-value="handleMonthSelect">
                        <template #button>
                            <span>{{ monthDisplayName }}</span>
                        </template>
                        <template #default>
                            <UiListboxOption v-for="monthIndex in MONTHS_COUNT" :key="monthIndex" :value="monthIndex - 1">
                                {{ getMonthName(monthIndex - 1) }}
                            </UiListboxOption>
                        </template>
                    </UiListbox>

                    <UiListbox v-model="calendarYear" @update:model-value="handleYearSelect">
                        <template #button>
                            <span>{{ calendarYear }}</span>
                        </template>
                        <template #default>
                            <UiListboxOption v-for="year in availableYears" :key="year" :value="year">
                                {{ year }}
                            </UiListboxOption>
                        </template>
                    </UiListbox>

                    <UiButtonAction @click="navigateMonth(1)">
                        <UiIcon name="chevron-right" class="text-blue-600 hover:text-blue-800" />
                    </UiButtonAction>
                </div>

                <!-- Calendar Grid -->
                <div class="grid grid-cols-7 items-center justify-center gap-y-1">
                    <!-- Weekday Headers -->
                    <span v-for="(day, index) in weekdays" :key="`weekday-${index}`" class="justify-self-center font-normal">
                        {{ format(day, 'EEEEE', { locale: de }) }}
                    </span>

                    <!-- Calendar Days -->
                    <button
                        v-for="(day, index) in days"
                        :key="`day-${format(day, 'yyyy-MM-dd')}-${index}`"
                        type="button"
                        :disabled="isDateDisabled(day)"
                        :class="getDayButtonClasses(day)"
                        @click="handleDaySelect(day)">
                        {{ format(day, 'dd') }}
                    </button>
                </div>
            </PopoverContent>
        </PopoverPortal>
    </PopoverRoot>
</template>

<script setup lang="ts">
    import { format, isSameMonth, parseISO, isSameDay, isBefore, isAfter, isValid, startOfDay, set, getDate } from 'date-fns'
    import { de } from 'date-fns/locale'
    import { useCalendarNavigation, useDateInput } from '~/composables/ui/date-picker'

    const props = defineProps<{
        disabled?: boolean
        min?: Date
        max?: Date
    }>()

    const emit = defineEmits<{
        (e: 'date-change', value: Date): void
    }>()

    const dateModel = defineModel<string | Date>()

    // Constants
    const ISO_DATE_FORMAT = 'yyyy-MM-dd'

    // Composables
    const { days, weekdays, selectedDay, currentMonthDate, selectDay } = useCalendarDates()

    const {
        dateInputValue,
        isTyping,
        isInternalUpdate,
        DATE_INPUT_MAX_LENGTH,
        handleInputChange: handleInputChangeBase,
        handleInputBlur: handleInputBlurBase,
        syncInputWithDate
    } = useDateInput()

    const {
        calendarMonth,
        calendarYear,
        monthDisplayName,
        availableYears,
        MONTHS_COUNT,
        getMonthName,
        updateCalendarNavigation,
        navigateMonth: navigateMonthBase,
        handleMonthSelect: handleMonthSelectBase,
        handleYearSelect: handleYearSelectBase
    } = useCalendarNavigation(currentMonthDate.value)

    // Reactive State
    const isPopoverOpen = ref(false)
    const dateInputRef = ref<HTMLInputElement | null>(null)

    function isDateDisabled(date: Date) {
        if (!props.min && !props.max) return false

        const dayStart = startOfDay(date)
        if (props.min && isBefore(dayStart, startOfDay(props.min))) return true
        if (props.max && isAfter(dayStart, startOfDay(props.max))) return true

        return false
    }

    // Utility functions
    const getDayButtonClasses = (day: Date): string => {
        const baseClasses =
            'h-8 w-8 cursor-pointer justify-self-center rounded-full p-0.5 hover:bg-red-100 disabled:cursor-default disabled:line-through disabled:hover:bg-transparent'

        const monthClasses = isSameMonth(day, currentMonthDate.value) ? 'font-normal' : 'opacity-70'

        const selectedClasses = isSameDay(day, selectedDay.value) ? 'bg-red-500 font-medium text-white' : ''

        return `${baseClasses} ${monthClasses} ${selectedClasses}`
    }

    const updateDateModel = (date: Date | null) => {
        dateModel.value = date ? format(date, ISO_DATE_FORMAT) : null
    }

    // Event Handlers
    const handlePopoverToggle = (open: boolean) => {
        isPopoverOpen.value = open

        if (open && dateModel.value) {
            let parsed = null

            if (typeof dateModel.value === 'string') {
                parsed = parseISO(dateModel.value)
            } else {
                parsed = dateModel.value
            }

            if (isValid(parsed)) {
                selectDay(parsed)
            }
        }
    }

    const handleInputFocus = () => {
        if (!isPopoverOpen.value) {
            isPopoverOpen.value = true
        }
    }

    const handleInputChange = (event: Event) => {
        handleInputChangeBase(event, (date: Date | null) => {
            if (date) {
                updateDateModel(date)
                selectDay(date)
            } else {
                updateDateModel(null)
            }
        })
    }

    const handleInputBlur = () => {
        handleInputBlurBase(selectedDay.value, (date: Date) => {
            if(date) {
                updateDateModel(date)
                selectDay(date)
            } else {
                updateDateModel(null)

            }
        })
    }

    const navigateMonth = (direction: number) => {
        const newDate = navigateMonthBase(direction)
        currentMonthDate.value = newDate
    }

    const handleMonthSelect = (newMonth: number) => {
        const newDate = handleMonthSelectBase(newMonth)
        currentMonthDate.value = newDate
    }

    const handleYearSelect = (newYear: number) => {
        const newDate = handleYearSelectBase(newYear)
        currentMonthDate.value = newDate
    }

    const handleDaySelect = (day: Date) => {
        const targetDate = set(day, { date: getDate(day) })
        selectDay(targetDate)
        updateDateModel(targetDate)
        isPopoverOpen.value = false
        emit('date-change', targetDate)
    }

    // Watchers
    watch(
        currentMonthDate,
        (newDate) => {
            updateCalendarNavigation(newDate)
        },
        { immediate: true }
    )

    watch(
        dateModel,
        (newValue) => {
            if(newValue === null) {
                dateInputValue.value = null
            }
            if (newValue && !isInternalUpdate.value && !isTyping.value) {
                const parsed = parseISO(newValue.toString())
                if (isValid(parsed)) {
                    selectDay(parsed)
                    if (!isTyping.value) {
                    syncInputWithDate(parsed)
                    }
                }
            }
        },
        { immediate: true }
    )

    watch(selectedDay, (newDay) => {
        if (newDay && !isInternalUpdate.value) {
            updateDateModel(newDay)
            if (!isTyping.value) {
                syncInputWithDate(newDay)
            }
        }
    })

    // Public API
    const focus = () => {
        dateInputRef.value?.focus()
    }

    defineExpose({ focus })
</script>
