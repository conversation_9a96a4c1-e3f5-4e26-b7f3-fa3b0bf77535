<template>
    <PopoverRoot :open="openPopover" @update:open="(e) => handleUpdateOpen(e)">
        <PopoverTrigger aria-label="Select hour and minutes" asChild :class="{ 'pointer-events-none': disabled }">
            <div class="relative flex items-center">
                <input
                    type="text"
                    inputmode="none"
                    autocomplete="off"
                    placeholder="HH:MM"
                    ref="timeInputRef"
                    :value="inputValue"
                    class="form-input"
                    :disabled="disabled"
                    @click="handleInputClick"
                    @input="handleInput"
                    @focus="handleInputFocus"
                    @keydown="handleInputKeydown"
                    @paste="handlePaste" />
                <UiIcon class="absolute right-0 h-8 w-8 pr-3" name="clock" />
            </div>
        </PopoverTrigger>

        <PopoverPortal v-if="!disabled">
            <PopoverContent
                ref="popoverContentRef"
                side="bottom"
                class="z-[1000] w-[20rem] md:w-full rounded-sm bg-white p-2 text-sm font-light shadow-xl"
                @openAutoFocus="(e) => e.preventDefault()">
                <div class="grid grid-cols-3">
                    <div class="border-grey-100 col-span-2 border-r md:py-2 md:pl-2 md:pr-4">
                        <p class="mb-4 text-center font-normal">Stunde</p>
                        <div class="grid grid-cols-6 gap-1">
                            <button
                                v-for="(hour, i) in hourOptions"
                                class="h-8 w-8 cursor-pointer rounded-full p-0.5 hover:bg-red-100"
                                :class="`${hour === currentHour ? 'bg-red-500 font-medium text-white' : ''}`"
                                @click="() => handleSelectHour(hour)"
                                :key="hour + i.toString()">
                                {{ hour }}
                            </button>
                        </div>
                    </div>
                    <div class="md:py-2 md:pl-4 md:pr-2">
                        <p class="mb-4 text-center font-normal">Minuten</p>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-1">
                            <button
                                v-for="(minute, i) in minutesOptions"
                                class="h-8 w-8 cursor-pointer rounded-full p-0.5 hover:bg-red-100"
                                :class="`${minute === currentMinutes ? 'bg-red-500 font-medium text-white' : ''}`"
                                @click="() => handleSelectMinutes(minute)"
                                :key="minute + i.toString()">
                                {{ minute }}
                            </button>
                        </div>
                    </div>
                </div>
            </PopoverContent>
        </PopoverPortal>
    </PopoverRoot>
</template>

<script setup lang="ts">
    import { useTimePicker } from '~/composables/ui/time-picker'
    import { onClickOutside } from '@vueuse/core'

    defineProps<{
        disabled?: boolean
    }>()

    const handleInputClick = (e: MouseEvent) => {
        e.stopPropagation()
        openPopover.value = true
    }

    const modelValue = defineModel<string>()

    const emit = defineEmits<{
        (e: 'time-selected'): void
    }>()

    const isTimeChanged = ref(false)
    const openPopover = ref(false)
    const timeInputRef = ref<HTMLInputElement | null>(null)
    const inputValue = ref(modelValue.value || '')
    const isUpdatingFromPopover = ref(false)
    const popoverContentRef = ref<HTMLElement | null>(null)

    // Create a dummy ref for the composable that we don't use for auto-completion
    const composableModelValue = ref('')
    const { hourOptions, minutesOptions } = useTimePicker(composableModelValue)

    // Parse current input for popover highlighting
    const currentHour = computed(() => {
        const match = inputValue.value.match(/^(\d{2}):/)
        return match ? match[1] : null
    })

    const currentMinutes = computed(() => {
        const match = inputValue.value.match(/^\d{2}:(\d{2})$/)
        return match ? match[1] : null
    })

    defineExpose({
        timeInputRef
    })

    onClickOutside(popoverContentRef, (event) => {
        const target = event.target as Element
        const isClickOnInput = timeInputRef.value?.contains(target)
        const isClickOnIcon = target?.closest('.absolute.right-0')

        if (!isClickOnInput && !isClickOnIcon && openPopover.value) {
            openPopover.value = false
        }
    }, {
        detectIframe: true,
        ignore: [timeInputRef]
    })

    const handleUpdateOpen = (open: boolean) => {
        openPopover.value = open

        if (!open && isTimeChanged.value) {
            emit('time-selected')
            isTimeChanged.value = false
        }
    }

    const handleInputFocus = () => !openPopover.value && (openPopover.value = true)


    const handleInput = (e: Event) => {
        if (isUpdatingFromPopover.value) return

        const target = e.target as HTMLInputElement
        inputValue.value = target.value

        // Handle different input states
        if (target.value === '') {
            // Empty input - clear modelValue
            if (modelValue.value !== '') {
                modelValue.value = ''
                isTimeChanged.value = true
            }
        } else if (/^\d{2}:\d{2}$/.test(target.value)) {
            // Complete time format - validate and update if valid
            if (isValidCompleteTime(target.value)) {
                if (modelValue.value !== target.value) {
                    modelValue.value = target.value
                    isTimeChanged.value = true
                }
            } else {
                // If the complete time is invalid, revert to previous valid value
                nextTick(() => {
                    target.value = modelValue.value || ''
                    inputValue.value = target.value
                })
            }
        } else {
            // Incomplete time format (like "10:" or "1" or "10:5") - clear modelValue
            if (modelValue.value !== '') {
                modelValue.value = ''
                isTimeChanged.value = true
            }
        }
    }

    const handleSelectHour = (hour: string) => {
        isUpdatingFromPopover.value = true
        const minutes = currentMinutes.value || '00'
        const newTime = `${hour}:${minutes}`
        inputValue.value = newTime
        modelValue.value = newTime
        isTimeChanged.value = true
        nextTick(() => {
            isUpdatingFromPopover.value = false
        })
    }

    const handleSelectMinutes = (minute: string) => {
        isUpdatingFromPopover.value = true
        const hours = currentHour.value || '00'
        const newTime = `${hours}:${minute}`
        inputValue.value = newTime
        modelValue.value = newTime
        isTimeChanged.value = true
        nextTick(() => {
            isUpdatingFromPopover.value = false
        })
    }

    const handlePaste = (e: ClipboardEvent) => {
        e.preventDefault()
        const pastedData = e.clipboardData?.getData('text') || ''

        if (isValidCompleteTime(pastedData)) {
            inputValue.value = pastedData
            modelValue.value = pastedData
            isTimeChanged.value = true
        }
    }

    const isValidTimeChar = (char: string, position: number, currentValue: string): boolean => {
        if (position === 0) {
            // First hour digit: 0-2
            return /[0-2]/.test(char)
        } else if (position === 1) {
            // Second hour digit: if first is 2, then 0-3, otherwise 0-9
            const firstDigit = currentValue[0]
            return firstDigit === '2' ? /[0-3]/.test(char) : /[0-9]/.test(char)
        } else if (position === 2) {
            // Must be colon
            return char === ':'
        } else if (position === 3) {
            // First minute digit: 0-5
            return /[0-5]/.test(char)
        } else if (position === 4) {
            // Second minute digit: 0-9
            return /[0-9]/.test(char)
        }
        return false
    }

    const isValidCompleteTime = (timeString: string): boolean => {
        // Check if it matches HH:MM format
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/
        return timeRegex.test(timeString)
    }

    const handleInputKeydown = (e: KeyboardEvent) => {
        const allowedControlKeys = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete', 'Home', 'End']
        const input = e.target as HTMLInputElement
        const value = input.value
        const cursorPos = input.selectionStart || 0

        // Allow space to be prevented
        if (e.code === 'Space') {
            e.preventDefault()
            return
        }

        // Allow control/navigation keys
        if (allowedControlKeys.includes(e.key)) return

        // Handle Ctrl/Cmd combinations
        if (e.ctrlKey || e.metaKey) return

        // Only allow digits and colon
        if (!/^[0-9:]$/.test(e.key)) {
            e.preventDefault()
            return
        }

        // Handle typing
        if (/^[0-9]$/.test(e.key)) {
            // Auto-insert colon when typing third character
            if (cursorPos === 2 && !value.includes(':')) {
                e.preventDefault()
                const newValue = value + ':' + e.key
                input.value = newValue
                input.setSelectionRange(4, 4)
                inputValue.value = newValue
                return
            }

            // Validate the character at this position
            if (!isValidTimeChar(e.key, cursorPos, value)) {
                e.preventDefault()
                return
            }

            // Additional validation for complete time when we have enough characters
            const newValue = value.slice(0, cursorPos) + e.key + value.slice(cursorPos + 1)

            // If we're completing the time (5 characters), validate the complete time
            if (newValue.length === 5 && newValue.includes(':')) {
                if (!isValidCompleteTime(newValue)) {
                    e.preventDefault()
                    return
                }
            }

            // Prevent typing beyond 5 characters
            if (value.length >= 5 && cursorPos >= value.length) {
                e.preventDefault()
                return
            }
        }

        // Handle colon
        if (e.key === ':') {
            // Only allow colon at position 2
            if (cursorPos !== 2 || value.includes(':')) {
                e.preventDefault()
                return
            }
        }
    }

    // Sync inputValue with modelValue on external changes (but not from popover)
    watch(modelValue, (newValue) => {
        if (!isUpdatingFromPopover.value && newValue !== inputValue.value) {
            inputValue.value = newValue || ''
        }
    })

    // Initialize inputValue from modelValue
    watch(modelValue, (newValue) => {
        if (inputValue.value === '' && newValue) {
            inputValue.value = newValue
        }
    }, { immediate: true })
</script>

<style scoped>
    input[type='time']::-webkit-calendar-picker-indicator,
    input::-webkit-calendar-picker-indicator,
    input::-webkit-inner-spin-button,
    input::-webkit-outer-spin-button {
        display: none !important;
        -webkit-appearance: none;
        margin: 0;
    }

    input {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: textfield;
    }
</style>
